<!DOCTYPE html>
<html class="no-js" lang="zxx">

<head>
    <!-- Basic metas
        ======================================== -->
    <meta charset="utf-8">
    <meta name="author" content="">
    <meta name="description" content="">
    <meta name="keywords" content="">
    <!-- Mobile specific metas
        ======================================== -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="template-color" content="#ffffff">
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Documentation" />

    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">


    <!-- Page Title
        ======================================== -->
    <title>Elever - Construction Building & Renovation HTML Template</title>


    <meta name="template-color" content="#ffffff">
    <!-- Icon fonts
        ======================================== -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,500i,700,700i,900" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="assets/css/fontawesome-all.min.css">
    <!-- Bootstrap -->
    <link rel="stylesheet" type="text/css" href="assets/css/vendor/bootstrap.min.css">
    <!-- Custom css -->
    <link rel="stylesheet" type="text/css" href="assets/css/main.css">
</head>

<body>

    <!-- Header starts -->
    <a href="#top" class="back-to-top"><i class="fal fa-arrow-up"></i></a>
    <header class="page-header" id="top">
        <div class="container-fluid">
            <h3 class="text-center">Elever - Construction Building & Renovation HTML Template</h3>
        </div>
        <!-- End of .container -->
    </header>
    <!-- End of .page-header -->
    <!-- End of .main-content -->
    <div class="main-content-wrapper">
        <div class="container-fluid">
            <div class="main-content p-t-40">
                <aside class="sidebar-navigation">
                    <nav class="nav">
                        <div class="logo-wrapper m-b-30">
                            <img src="assets/images/logo.svg" alt="logo">
                        </div>
                        <form action="#" class="search-form m-b-20">
                            <div class="input-group">
                                <input type="text" class="search-input" placeholder="Search item">
                            </div>
                        </form>
                        <ol class="doc-nav">
                            <li>
                                <a class="nav-link" href="#section-1">Introduction</a>
                                <ol class="sub-nav">
                                    <li><a class="nav-link" href="#section-1-1">What's Included</a></li>
                                    <li><a class="nav-link" href="#section-1-3">Font</a></li>
                                    <li><a class="nav-link" href="#section-1-4">Source File (CSS)</a></li>
                                    <li><a class="nav-link" href="#section-1-5">Source File (JS)</a></li>
                                </ol>
                            </li>
                            <li>
                                <a class="nav-link" href="#section-2-1">Basic Site Settings</a>
                                <ol class="sub-nav">
                                    <li><a class="nav-link" href="#section-2-1">Title and Favicon</a></li>
                                    <li><a class="nav-link" href="#section-2-2">Logo</a></li>
                                    <li><a class="nav-link" href="#section-2-3">Sidebar Menu</a></li>
                                    <li><a class="nav-link" href="#section-2-4">Topbar Menu</a></li>
                                </ol>
                            </li>
                            <li>
                                <a class="nav-link" href="#section-3-1">Template Options</a>
                                <ol class="sub-nav">
                                    <li><a class="nav-link" href="#section-3-1">Header</a></li>
                                    <li><a class="nav-link" href="#section-3-2">Footer</a></li>
                                    <li><a class="nav-link" href="#section-3-3">Breadcrumb</a></li>
                                    <li><a class="nav-link" href="#section-3-4">Global Color</a></li>
                                </ol>
                            </li>
                            <li>
                                <a class="nav-link" href="#section-4">Page Content</a>
                                <ol class="sub-nav">
                                    <li><a class="nav-link" href="#section-4-1">Blog</a></li>
                                    <li><a class="nav-link" href="#section-4-2">Testimonial</a></li>
                                    <li><a class="nav-link" href="#section-4-3">Brand Slider</a></li>
                                </ol>
                            </li>
                            <li><a class="nav-link" href="#section-5">Support</a></li>
                        </ol>
                    </nav>
                </aside>
                <main class="main">
                    <div class="filterable-wrapper">
                        <div className="banner-wrapper mb-30">
                            <img src="assets/images/banner.png" alt="banner">
                        </div>

                        <!-- Start of #section-1 -->
                        <section id="section-1">
                            <h3 class="mt-0">1. Introduction</h3>
                            <p>We would like to thank you for choosing Elever - Construction Building &
                                Renovation HTML Template.
                            </p>
                            <p>We made Elever from the ground-up with flexibility in mind. Each element of Elever
                                is extremely customizable, where you can make <strong>Elever</strong> to reflect your
                                own branding styles.</p>
                            <p> The guide gives you detailed methodologies about how you can customize Elever and
                                make it fit your brand perfectly! </p>
                        </section>

                        <!-- Start of #section-1.1 -->
                        <section id="section-1-1">
                            <h3>1.1. What's Included</h3>

                            <p>After purchasing Elever template on themeforest.net with your Envato account, go to your
                                Download page. You can choose to download Elever template only or the entire
                                Elever template package which contains the following files:</p>

                            <img src="assets/images/doc/include.png" alt="Elever">
                            <ul>
                                <li><strong>Elever:</strong> A HTML template file.</li>
                                <li><strong>Documentation:</strong> This folder contains what you are reading now.</li>
                            </ul>


                        </section>


                        <!-- Start of #section-1.3 -->
                        <section id="section-1-3">
                            <h3>1.3. Font</h3>

                            <pre>
                                @import url('https://fonts.googleapis.com/css2?family=Gantari:ital,wght@0,100..900;1,100..900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
                        </pre>
                        </section>

                        <!-- Start of #section-1.4 -->
                        <section id="section-1-4">
                            <h3>1.4. Source File (CSS)</h3>
                            <pre>
                            &lt;link rel="stylesheet" preload href="assets/css/plugins/fontawesome.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/plugins/aos.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/plugins/odometer.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/plugins/swiper.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/plugins/metismenu.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/vendor/bootstrap.min.css"&gt;
                            &lt;link rel="stylesheet" preload href="assets/css/style.css"&gt;
                        </pre>
                        </section>

                        <!-- Start of #section-1.5 -->
                        <section id="section-1-5">
                            <h3>1.5. Source File (JS)</h3>
                            <pre>

                            &lt;script src="assets/js/plugins/jquery.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/vendor/bootstrap.min.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/odometer.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/jquery-appear.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/metismenu.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/swiper.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/aos.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/nice-select.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/smooth-scroll.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/vendor/waw.js"&gt;&lt;/script&gt;
                            &lt;script defer src="assets/js/vendor/contact-form.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/vendor/map-content.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/vendor/info-box.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/plugins/contact.form.js"&gt;&lt;/script&gt;
                            &lt;script src="assets/js/main.js"&gt;&lt;/script&gt;
                        </pre>
                        </section>

                        <!-- Start of #section-2.1 -->
                        <section id="section-2-1">
                            <h3>2.1. Change Site Title and Favicon</h3>
                            <p>To change your Site title and Favicon, open the Elever in your editor and go to the
                                location
                                by following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/title.png" alt="doc">
                        </section>

                        <!-- Start of #section-2.1 -->
                        <section id="section-2-1">
                            <h3>2.15. Set Default RTL (Right To Left)</h3>
                            <p>You can manually set the default text direction to right-to-left by adding
                                <b style="color: #3045ff;">dir="rtl"</b> to
                                the

                                <b style="color: #3045ff;">body</b>
                                tag. <br>
                                <b>Or :-- You can pest this code bottom of main.js file</b> <br>

                                <code
                                    style="background: #eeeeee; margin-top: 20px; display: block;     max-width: max-content; padding: 25px;">
                                        window.onload = function () { <br>
                                            document.body.setAttribute("dir", "rtl"); <br>
                                        };
                                </code>
                            </p>
                            <img src="assets/images/doc/rtl1.png" alt="doc">
                        </section>

                        <!-- Start of #section-2.2 -->
                        <section id="section-2-2">
                            <h3>2.2. Change Logo</h3>
                            <p>To change your Site Logo, open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/logo.png" alt="doc">
                        </section>

                        <!-- Start of #section-2.3 -->
                        <section id="section-2-3">
                            <h3>2.3. Header Menu</h3>
                            <p>To customize your Header Menu, open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/menu-header.png" alt="doc">
                        </section>

                        <!-- Start of #section-2.4 -->
                        <section id="section-2-4">
                            <h3>2.4. Sidebar Menu</h3>
                            <p>To customize your Sidebar Menu, open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/menu-sidebar.png" alt="doc">
                        </section>

                        <!-- Start of #section-3.1 -->
                        <section id="section-3-1">
                            <h3>3.1. Header</h3>
                            <p>From the located file in the image you can customize you site header layout</p>
                            <img src="assets/images/doc/header.png" alt="doc">
                        </section>

                        <!-- Start of #section-3.2 -->
                        <section id="section-3-2">
                            <h3>3.2. Footer</h3>
                            <p>Here is the file where you can customize your footer</p>
                            <img src="assets/images/doc/footer.png" alt="doc">
                        </section>


                        <!-- Start of #section-3.3 -->
                        <section id="section-3-4">
                            <h3>3.3. Global Color</h3>
                            <p>To change Elever Global Color</p>
                            <img src="assets/images/doc/color.png" alt="doc">
                        </section>

                        <!-- Start of #section-4 -->
                        <section id="section-4">
                            <h3>4. Content</h3>
                            <p>To customize your Site Content open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <p><strong>Page Content:</strong></p>
                            <img src="assets/images/doc/content.png" alt="doc">
                        </section>

                        <!-- Start of #section-4.6 -->
                        <section id="section-4-1">
                            <h3>4.1. Blog</h3>
                            <p>To customize your Site Blog open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/blog1.png" alt="doc">
                            <img src="assets/images/doc/blog2.png" alt="doc">
                        </section>


                        <!-- Start of #section-4.9 -->
                        <section id="section-4-2">
                            <h3>4.2. Testimonial</h3>
                            <p>To customize your Site Testimonial open the Elever in your editor and go to the location
                                by following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/testimonial2.png" alt="doc">
                            <img src="assets/images/doc/testimonial1.png" alt="doc">
                        </section>

                        <!-- Start of #section-4.10 -->
                        <section id="section-4-3">
                            <h3>4.3. Brand Slider</h3>
                            <p>To customize your Brand Slider open the Elever in your editor and go to the location by
                                following screenshot which are given bellow.</p>
                            <img src="assets/images/doc/brand_slider-1.png" alt="doc">
                            <img src="assets/images/doc/brand_slider-2.png" alt="doc">
                        </section>


                        <!-- Start of #section-5 -->
                        <section id="section-5">
                            <h3>5. Support</h3>
                            <h4>Support Scope</h4>
                            <p><strong>Included in Free Support Scope:</strong></p>
                            <ul>
                                <li>Fixing bugs</li>
                                <li>Helping clients in changing site contents by sending instructions that couldn’t be
                                    covered by documentation
                                </li>
                            </ul>
                            <p><strong><strong style="color:red">NOT</strong> Included in Free Support Scope:</strong>
                            </p>
                            <ul>
                                <li>Any type of custom changes</li>
                                <li>Any type of request to update clients’ site contents</li>
                            </ul>
                            <p><strong>Paid Support Scope:</strong></p>
                            <p>Paid support is not a part of Free Support when you purchase the template from
                                themeforest.
                                We understand that some clients need custom changes while using our template. For that
                                we
                                kept an option for paid support which includes:</p>
                            <ul>
                                <li>Custom change request</li>
                                <li>Custom pages</li>
                                <li>Custom features</li>
                            </ul>
                            <p>We kept a flat rate for our paid support which is $25/hour.</p>

                            <h4>Support Ticket</h4>
                            <p>Please send your <a target="_blank" href="https://themewant.com/envato-support/">support
                                    request
                                    here</a>.
                                You will response within 1
                                business day.</p>
                        </section>

                        <section>
                            <p style="color:blue">Regards, <br>ThemeWant Support Team</p>
                        </section>
                    </div>
                    <!-- End of .filterable-wrapper -->
                </main>
            </div>
            <!-- End of .main-content -->
        </div>
        <!-- End of .container -->
    </div>
    <!-- End of .main-content -->
    <footer class="page-footer">
        <div class="container-fluid">
            <p>Designed by <a href="https://themewant.com/">ThemeWant</a>.</p>
        </div>
        <!-- End of .page-footer -->
    </footer>
    <!-- End of .page-footer -->
    <!-- Javascripts
    ======================================= -->
    <!-- jQuery -->
    <script src="assets/js/vendor/jquery.min.js"></script>
    <script src="assets/js/vendor/jquery-migrate.min.js"></script>
    <script src="assets/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- jQuery Easing Plugin -->
    <script src="assets/js/vendor/easing-1.3.js"></script>
    <!-- Custom Script -->
    <script src="assets/js/main.js"></script>
    <script>
        if (/MSIE \d|Trident.*rv:/.test(navigator.userAgent)) {
            document.write('<script src="https://cdn.jsdelivr.net/npm/css-vars-ponyfill@1"><\/script>');
            document.write('<script src="assets/js/ie.js"><\/script>');
        }
    </script>
</body>

</html>