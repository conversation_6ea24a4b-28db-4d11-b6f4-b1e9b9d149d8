:root {
    --primary-font: 'Roboto', sans-serif;
    --primary-color: #0dc270;
    --theme-color: #50D8D1;
    --action-color: #f84e1d;
    --typo-dark: #111111;
    --typo-body: #444444;
    --typo-mid: #8A94A6;
    --typo-light: #A6AEBC;
    --bg-light: #F8F9FB;
    --color-success: #00865A;
    --color-alert: #F07300;
    --color-info: #0D55CF;
    --color-error: #DD2727;
    --radius: 4px;
    --radius-big: 6px;
    --radius-small: 2px;
    --w-light: 300;
    --w-regular: 400;
    --w-medium: 500;
    --w-semi-bold: 600;
    --w-bold: 700;
    --w-extra-bold: 800;
    --w-black: 900
}



.m-0 {
    margin: 0rem
}

.p-0 {
    padding: 0rem
}

.m-t-0 {
    margin-top: 0rem
}

.p-t-0 {
    padding-top: 0rem
}

@media (max-width: 991px) {
    .lg-m-0 {
        margin: 0rem !important
    }

    .lg-p-0 {
        padding: 0rem !important
    }

    .lg-m-t-0 {
        margin-top: 0rem !important
    }

    .lg-p-t-0 {
        padding-top: 0rem !important
    }
}

@media (max-width: 767px) {
    .md-m-0 {
        margin: 0rem !important
    }

    .md-p-0 {
        padding: 0rem !important
    }

    .md-m-t-0 {
        margin-top: 0rem !important
    }

    .md-p-t-0 {
        padding-top: 0rem !important
    }
}

.m-0 {
    margin: 0rem
}

.p-0 {
    padding: 0rem
}

.m-r-0 {
    margin-right: 0rem
}

.p-r-0 {
    padding-right: 0rem
}

@media (max-width: 991px) {
    .lg-m-0 {
        margin: 0rem !important
    }

    .lg-p-0 {
        padding: 0rem !important
    }

    .lg-m-r-0 {
        margin-right: 0rem !important
    }

    .lg-p-r-0 {
        padding-right: 0rem !important
    }
}

@media (max-width: 767px) {
    .md-m-0 {
        margin: 0rem !important
    }

    .md-p-0 {
        padding: 0rem !important
    }

    .md-m-r-0 {
        margin-right: 0rem !important
    }

    .md-p-r-0 {
        padding-right: 0rem !important
    }
}

.m-0 {
    margin: 0rem
}

.p-0 {
    padding: 0rem
}

.m-b-0 {
    margin-bottom: 0rem
}

.p-b-0 {
    padding-bottom: 0rem
}

@media (max-width: 991px) {
    .lg-m-0 {
        margin: 0rem !important
    }

    .lg-p-0 {
        padding: 0rem !important
    }

    .lg-m-b-0 {
        margin-bottom: 0rem !important
    }

    .lg-p-b-0 {
        padding-bottom: 0rem !important
    }
}

@media (max-width: 767px) {
    .md-m-0 {
        margin: 0rem !important
    }

    .md-p-0 {
        padding: 0rem !important
    }

    .md-m-b-0 {
        margin-bottom: 0rem !important
    }

    .md-p-b-0 {
        padding-bottom: 0rem !important
    }
}

.m-0 {
    margin: 0rem
}

.p-0 {
    padding: 0rem
}

.m-l-0 {
    margin-left: 0rem
}

.p-l-0 {
    padding-left: 0rem
}

@media (max-width: 991px) {
    .lg-m-0 {
        margin: 0rem !important
    }

    .lg-p-0 {
        padding: 0rem !important
    }

    .lg-m-l-0 {
        margin-left: 0rem !important
    }

    .lg-p-l-0 {
        padding-left: 0rem !important
    }
}

@media (max-width: 767px) {
    .md-m-0 {
        margin: 0rem !important
    }

    .md-p-0 {
        padding: 0rem !important
    }

    .md-m-l-0 {
        margin-left: 0rem !important
    }

    .md-p-l-0 {
        padding-left: 0rem !important
    }
}

.m-5 {
    margin: .5rem
}

.p-5 {
    padding: .5rem
}

.m-t-5 {
    margin-top: .5rem
}

.p-t-5 {
    padding-top: .5rem
}

@media (max-width: 991px) {
    .lg-m-5 {
        margin: .5rem !important
    }

    .lg-p-5 {
        padding: .5rem !important
    }

    .lg-m-t-5 {
        margin-top: .5rem !important
    }

    .lg-p-t-5 {
        padding-top: .5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-5 {
        margin: .5rem !important
    }

    .md-p-5 {
        padding: .5rem !important
    }

    .md-m-t-5 {
        margin-top: .5rem !important
    }

    .md-p-t-5 {
        padding-top: .5rem !important
    }
}

.m-5 {
    margin: .5rem
}

.p-5 {
    padding: .5rem
}

.m-r-5 {
    margin-right: .5rem
}

.p-r-5 {
    padding-right: .5rem
}

@media (max-width: 991px) {
    .lg-m-5 {
        margin: .5rem !important
    }

    .lg-p-5 {
        padding: .5rem !important
    }

    .lg-m-r-5 {
        margin-right: .5rem !important
    }

    .lg-p-r-5 {
        padding-right: .5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-5 {
        margin: .5rem !important
    }

    .md-p-5 {
        padding: .5rem !important
    }

    .md-m-r-5 {
        margin-right: .5rem !important
    }

    .md-p-r-5 {
        padding-right: .5rem !important
    }
}

.m-5 {
    margin: .5rem
}

.p-5 {
    padding: .5rem
}

.m-b-5 {
    margin-bottom: .5rem
}

.p-b-5 {
    padding-bottom: .5rem
}

@media (max-width: 991px) {
    .lg-m-5 {
        margin: .5rem !important
    }

    .lg-p-5 {
        padding: .5rem !important
    }

    .lg-m-b-5 {
        margin-bottom: .5rem !important
    }

    .lg-p-b-5 {
        padding-bottom: .5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-5 {
        margin: .5rem !important
    }

    .md-p-5 {
        padding: .5rem !important
    }

    .md-m-b-5 {
        margin-bottom: .5rem !important
    }

    .md-p-b-5 {
        padding-bottom: .5rem !important
    }
}

.m-5 {
    margin: .5rem
}

.p-5 {
    padding: .5rem
}

.m-l-5 {
    margin-left: .5rem
}

.p-l-5 {
    padding-left: .5rem
}

@media (max-width: 991px) {
    .lg-m-5 {
        margin: .5rem !important
    }

    .lg-p-5 {
        padding: .5rem !important
    }

    .lg-m-l-5 {
        margin-left: .5rem !important
    }

    .lg-p-l-5 {
        padding-left: .5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-5 {
        margin: .5rem !important
    }

    .md-p-5 {
        padding: .5rem !important
    }

    .md-m-l-5 {
        margin-left: .5rem !important
    }

    .md-p-l-5 {
        padding-left: .5rem !important
    }
}

.m-10 {
    margin: 1rem
}

.p-10 {
    padding: 1rem
}

.m-t-10 {
    margin-top: 1rem
}

.p-t-10 {
    padding-top: 1rem
}

@media (max-width: 991px) {
    .lg-m-10 {
        margin: 1rem !important
    }

    .lg-p-10 {
        padding: 1rem !important
    }

    .lg-m-t-10 {
        margin-top: 1rem !important
    }

    .lg-p-t-10 {
        padding-top: 1rem !important
    }
}

@media (max-width: 767px) {
    .md-m-10 {
        margin: 1rem !important
    }

    .md-p-10 {
        padding: 1rem !important
    }

    .md-m-t-10 {
        margin-top: 1rem !important
    }

    .md-p-t-10 {
        padding-top: 1rem !important
    }
}

.m-10 {
    margin: 1rem
}

.p-10 {
    padding: 1rem
}

.m-r-10 {
    margin-right: 1rem
}

.p-r-10 {
    padding-right: 1rem
}

@media (max-width: 991px) {
    .lg-m-10 {
        margin: 1rem !important
    }

    .lg-p-10 {
        padding: 1rem !important
    }

    .lg-m-r-10 {
        margin-right: 1rem !important
    }

    .lg-p-r-10 {
        padding-right: 1rem !important
    }
}

@media (max-width: 767px) {
    .md-m-10 {
        margin: 1rem !important
    }

    .md-p-10 {
        padding: 1rem !important
    }

    .md-m-r-10 {
        margin-right: 1rem !important
    }

    .md-p-r-10 {
        padding-right: 1rem !important
    }
}

.m-10 {
    margin: 1rem
}

.p-10 {
    padding: 1rem
}

.m-b-10 {
    margin-bottom: 1rem
}

.p-b-10 {
    padding-bottom: 1rem
}

@media (max-width: 991px) {
    .lg-m-10 {
        margin: 1rem !important
    }

    .lg-p-10 {
        padding: 1rem !important
    }

    .lg-m-b-10 {
        margin-bottom: 1rem !important
    }

    .lg-p-b-10 {
        padding-bottom: 1rem !important
    }
}

@media (max-width: 767px) {
    .md-m-10 {
        margin: 1rem !important
    }

    .md-p-10 {
        padding: 1rem !important
    }

    .md-m-b-10 {
        margin-bottom: 1rem !important
    }

    .md-p-b-10 {
        padding-bottom: 1rem !important
    }
}

.m-10 {
    margin: 1rem
}

.p-10 {
    padding: 1rem
}

.m-l-10 {
    margin-left: 1rem
}

.p-l-10 {
    padding-left: 1rem
}

@media (max-width: 991px) {
    .lg-m-10 {
        margin: 1rem !important
    }

    .lg-p-10 {
        padding: 1rem !important
    }

    .lg-m-l-10 {
        margin-left: 1rem !important
    }

    .lg-p-l-10 {
        padding-left: 1rem !important
    }
}

@media (max-width: 767px) {
    .md-m-10 {
        margin: 1rem !important
    }

    .md-p-10 {
        padding: 1rem !important
    }

    .md-m-l-10 {
        margin-left: 1rem !important
    }

    .md-p-l-10 {
        padding-left: 1rem !important
    }
}

.m-15 {
    margin: 1.5rem
}

.p-15 {
    padding: 1.5rem
}

.m-t-15 {
    margin-top: 1.5rem
}

.p-t-15 {
    padding-top: 1.5rem
}

@media (max-width: 991px) {
    .lg-m-15 {
        margin: 1.5rem !important
    }

    .lg-p-15 {
        padding: 1.5rem !important
    }

    .lg-m-t-15 {
        margin-top: 1.5rem !important
    }

    .lg-p-t-15 {
        padding-top: 1.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-15 {
        margin: 1.5rem !important
    }

    .md-p-15 {
        padding: 1.5rem !important
    }

    .md-m-t-15 {
        margin-top: 1.5rem !important
    }

    .md-p-t-15 {
        padding-top: 1.5rem !important
    }
}

.m-15 {
    margin: 1.5rem
}

.p-15 {
    padding: 1.5rem
}

.m-r-15 {
    margin-right: 1.5rem
}

.p-r-15 {
    padding-right: 1.5rem
}

@media (max-width: 991px) {
    .lg-m-15 {
        margin: 1.5rem !important
    }

    .lg-p-15 {
        padding: 1.5rem !important
    }

    .lg-m-r-15 {
        margin-right: 1.5rem !important
    }

    .lg-p-r-15 {
        padding-right: 1.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-15 {
        margin: 1.5rem !important
    }

    .md-p-15 {
        padding: 1.5rem !important
    }

    .md-m-r-15 {
        margin-right: 1.5rem !important
    }

    .md-p-r-15 {
        padding-right: 1.5rem !important
    }
}

.m-15 {
    margin: 1.5rem
}

.p-15 {
    padding: 1.5rem
}

.m-b-15 {
    margin-bottom: 1.5rem
}

.p-b-15 {
    padding-bottom: 1.5rem
}

@media (max-width: 991px) {
    .lg-m-15 {
        margin: 1.5rem !important
    }

    .lg-p-15 {
        padding: 1.5rem !important
    }

    .lg-m-b-15 {
        margin-bottom: 1.5rem !important
    }

    .lg-p-b-15 {
        padding-bottom: 1.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-15 {
        margin: 1.5rem !important
    }

    .md-p-15 {
        padding: 1.5rem !important
    }

    .md-m-b-15 {
        margin-bottom: 1.5rem !important
    }

    .md-p-b-15 {
        padding-bottom: 1.5rem !important
    }
}

.m-15 {
    margin: 1.5rem
}

.p-15 {
    padding: 1.5rem
}

.m-l-15 {
    margin-left: 1.5rem
}

.p-l-15 {
    padding-left: 1.5rem
}

@media (max-width: 991px) {
    .lg-m-15 {
        margin: 1.5rem !important
    }

    .lg-p-15 {
        padding: 1.5rem !important
    }

    .lg-m-l-15 {
        margin-left: 1.5rem !important
    }

    .lg-p-l-15 {
        padding-left: 1.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-15 {
        margin: 1.5rem !important
    }

    .md-p-15 {
        padding: 1.5rem !important
    }

    .md-m-l-15 {
        margin-left: 1.5rem !important
    }

    .md-p-l-15 {
        padding-left: 1.5rem !important
    }
}

.m-20 {
    margin: 2rem
}

.p-20 {
    padding: 2rem
}

.m-t-20 {
    margin-top: 2rem
}

.p-t-20 {
    padding-top: 2rem
}

@media (max-width: 991px) {
    .lg-m-20 {
        margin: 2rem !important
    }

    .lg-p-20 {
        padding: 2rem !important
    }

    .lg-m-t-20 {
        margin-top: 2rem !important
    }

    .lg-p-t-20 {
        padding-top: 2rem !important
    }
}

@media (max-width: 767px) {
    .md-m-20 {
        margin: 2rem !important
    }

    .md-p-20 {
        padding: 2rem !important
    }

    .md-m-t-20 {
        margin-top: 2rem !important
    }

    .md-p-t-20 {
        padding-top: 2rem !important
    }
}

.m-20 {
    margin: 2rem
}

.p-20 {
    padding: 2rem
}

.m-r-20 {
    margin-right: 2rem
}

.p-r-20 {
    padding-right: 2rem
}

@media (max-width: 991px) {
    .lg-m-20 {
        margin: 2rem !important
    }

    .lg-p-20 {
        padding: 2rem !important
    }

    .lg-m-r-20 {
        margin-right: 2rem !important
    }

    .lg-p-r-20 {
        padding-right: 2rem !important
    }
}

@media (max-width: 767px) {
    .md-m-20 {
        margin: 2rem !important
    }

    .md-p-20 {
        padding: 2rem !important
    }

    .md-m-r-20 {
        margin-right: 2rem !important
    }

    .md-p-r-20 {
        padding-right: 2rem !important
    }
}

.m-20 {
    margin: 2rem
}

.p-20 {
    padding: 2rem
}

.m-b-20 {
    margin-bottom: 2rem
}

.p-b-20 {
    padding-bottom: 2rem
}

@media (max-width: 991px) {
    .lg-m-20 {
        margin: 2rem !important
    }

    .lg-p-20 {
        padding: 2rem !important
    }

    .lg-m-b-20 {
        margin-bottom: 2rem !important
    }

    .lg-p-b-20 {
        padding-bottom: 2rem !important
    }
}

@media (max-width: 767px) {
    .md-m-20 {
        margin: 2rem !important
    }

    .md-p-20 {
        padding: 2rem !important
    }

    .md-m-b-20 {
        margin-bottom: 2rem !important
    }

    .md-p-b-20 {
        padding-bottom: 2rem !important
    }
}

.m-20 {
    margin: 2rem
}

.p-20 {
    padding: 2rem
}

.m-l-20 {
    margin-left: 2rem
}

.p-l-20 {
    padding-left: 2rem
}

@media (max-width: 991px) {
    .lg-m-20 {
        margin: 2rem !important
    }

    .lg-p-20 {
        padding: 2rem !important
    }

    .lg-m-l-20 {
        margin-left: 2rem !important
    }

    .lg-p-l-20 {
        padding-left: 2rem !important
    }
}

@media (max-width: 767px) {
    .md-m-20 {
        margin: 2rem !important
    }

    .md-p-20 {
        padding: 2rem !important
    }

    .md-m-l-20 {
        margin-left: 2rem !important
    }

    .md-p-l-20 {
        padding-left: 2rem !important
    }
}

.m-25 {
    margin: 2.5rem
}

.p-25 {
    padding: 2.5rem
}

.m-t-25 {
    margin-top: 2.5rem
}

.p-t-25 {
    padding-top: 2.5rem
}

@media (max-width: 991px) {
    .lg-m-25 {
        margin: 2.5rem !important
    }

    .lg-p-25 {
        padding: 2.5rem !important
    }

    .lg-m-t-25 {
        margin-top: 2.5rem !important
    }

    .lg-p-t-25 {
        padding-top: 2.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-25 {
        margin: 2.5rem !important
    }

    .md-p-25 {
        padding: 2.5rem !important
    }

    .md-m-t-25 {
        margin-top: 2.5rem !important
    }

    .md-p-t-25 {
        padding-top: 2.5rem !important
    }
}

.m-25 {
    margin: 2.5rem
}

.p-25 {
    padding: 2.5rem
}

.m-r-25 {
    margin-right: 2.5rem
}

.p-r-25 {
    padding-right: 2.5rem
}

@media (max-width: 991px) {
    .lg-m-25 {
        margin: 2.5rem !important
    }

    .lg-p-25 {
        padding: 2.5rem !important
    }

    .lg-m-r-25 {
        margin-right: 2.5rem !important
    }

    .lg-p-r-25 {
        padding-right: 2.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-25 {
        margin: 2.5rem !important
    }

    .md-p-25 {
        padding: 2.5rem !important
    }

    .md-m-r-25 {
        margin-right: 2.5rem !important
    }

    .md-p-r-25 {
        padding-right: 2.5rem !important
    }
}

.m-25 {
    margin: 2.5rem
}

.p-25 {
    padding: 2.5rem
}

.m-b-25 {
    margin-bottom: 2.5rem
}

.p-b-25 {
    padding-bottom: 2.5rem
}

@media (max-width: 991px) {
    .lg-m-25 {
        margin: 2.5rem !important
    }

    .lg-p-25 {
        padding: 2.5rem !important
    }

    .lg-m-b-25 {
        margin-bottom: 2.5rem !important
    }

    .lg-p-b-25 {
        padding-bottom: 2.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-25 {
        margin: 2.5rem !important
    }

    .md-p-25 {
        padding: 2.5rem !important
    }

    .md-m-b-25 {
        margin-bottom: 2.5rem !important
    }

    .md-p-b-25 {
        padding-bottom: 2.5rem !important
    }
}

.m-25 {
    margin: 2.5rem
}

.p-25 {
    padding: 2.5rem
}

.m-l-25 {
    margin-left: 2.5rem
}

.p-l-25 {
    padding-left: 2.5rem
}

@media (max-width: 991px) {
    .lg-m-25 {
        margin: 2.5rem !important
    }

    .lg-p-25 {
        padding: 2.5rem !important
    }

    .lg-m-l-25 {
        margin-left: 2.5rem !important
    }

    .lg-p-l-25 {
        padding-left: 2.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-25 {
        margin: 2.5rem !important
    }

    .md-p-25 {
        padding: 2.5rem !important
    }

    .md-m-l-25 {
        margin-left: 2.5rem !important
    }

    .md-p-l-25 {
        padding-left: 2.5rem !important
    }
}

.m-30 {
    margin: 3rem
}

.p-30 {
    padding: 3rem
}

.m-t-30 {
    margin-top: 3rem
}

.p-t-30 {
    padding-top: 3rem
}

@media (max-width: 991px) {
    .lg-m-30 {
        margin: 3rem !important
    }

    .lg-p-30 {
        padding: 3rem !important
    }

    .lg-m-t-30 {
        margin-top: 3rem !important
    }

    .lg-p-t-30 {
        padding-top: 3rem !important
    }
}

@media (max-width: 767px) {
    .md-m-30 {
        margin: 3rem !important
    }

    .md-p-30 {
        padding: 3rem !important
    }

    .md-m-t-30 {
        margin-top: 3rem !important
    }

    .md-p-t-30 {
        padding-top: 3rem !important
    }
}

.m-30 {
    margin: 3rem
}

.p-30 {
    padding: 3rem
}

.m-r-30 {
    margin-right: 3rem
}

.p-r-30 {
    padding-right: 3rem
}

@media (max-width: 991px) {
    .lg-m-30 {
        margin: 3rem !important
    }

    .lg-p-30 {
        padding: 3rem !important
    }

    .lg-m-r-30 {
        margin-right: 3rem !important
    }

    .lg-p-r-30 {
        padding-right: 3rem !important
    }
}

@media (max-width: 767px) {
    .md-m-30 {
        margin: 3rem !important
    }

    .md-p-30 {
        padding: 3rem !important
    }

    .md-m-r-30 {
        margin-right: 3rem !important
    }

    .md-p-r-30 {
        padding-right: 3rem !important
    }
}

.m-30 {
    margin: 3rem
}

.p-30 {
    padding: 3rem
}

.m-b-30 {
    margin-bottom: 3rem
}

.p-b-30 {
    padding-bottom: 3rem
}

@media (max-width: 991px) {
    .lg-m-30 {
        margin: 3rem !important
    }

    .lg-p-30 {
        padding: 3rem !important
    }

    .lg-m-b-30 {
        margin-bottom: 3rem !important
    }

    .lg-p-b-30 {
        padding-bottom: 3rem !important
    }
}

@media (max-width: 767px) {
    .md-m-30 {
        margin: 3rem !important
    }

    .md-p-30 {
        padding: 3rem !important
    }

    .md-m-b-30 {
        margin-bottom: 3rem !important
    }

    .md-p-b-30 {
        padding-bottom: 3rem !important
    }
}

.m-30 {
    margin: 3rem
}

.p-30 {
    padding: 3rem
}

.m-l-30 {
    margin-left: 3rem
}

.p-l-30 {
    padding-left: 3rem
}

@media (max-width: 991px) {
    .lg-m-30 {
        margin: 3rem !important
    }

    .lg-p-30 {
        padding: 3rem !important
    }

    .lg-m-l-30 {
        margin-left: 3rem !important
    }

    .lg-p-l-30 {
        padding-left: 3rem !important
    }
}

@media (max-width: 767px) {
    .md-m-30 {
        margin: 3rem !important
    }

    .md-p-30 {
        padding: 3rem !important
    }

    .md-m-l-30 {
        margin-left: 3rem !important
    }

    .md-p-l-30 {
        padding-left: 3rem !important
    }
}

.m-35 {
    margin: 3.5rem
}

.p-35 {
    padding: 3.5rem
}

.m-t-35 {
    margin-top: 3.5rem
}

.p-t-35 {
    padding-top: 3.5rem
}

@media (max-width: 991px) {
    .lg-m-35 {
        margin: 3.5rem !important
    }

    .lg-p-35 {
        padding: 3.5rem !important
    }

    .lg-m-t-35 {
        margin-top: 3.5rem !important
    }

    .lg-p-t-35 {
        padding-top: 3.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-35 {
        margin: 3.5rem !important
    }

    .md-p-35 {
        padding: 3.5rem !important
    }

    .md-m-t-35 {
        margin-top: 3.5rem !important
    }

    .md-p-t-35 {
        padding-top: 3.5rem !important
    }
}

.m-35 {
    margin: 3.5rem
}

.p-35 {
    padding: 3.5rem
}

.m-r-35 {
    margin-right: 3.5rem
}

.p-r-35 {
    padding-right: 3.5rem
}

@media (max-width: 991px) {
    .lg-m-35 {
        margin: 3.5rem !important
    }

    .lg-p-35 {
        padding: 3.5rem !important
    }

    .lg-m-r-35 {
        margin-right: 3.5rem !important
    }

    .lg-p-r-35 {
        padding-right: 3.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-35 {
        margin: 3.5rem !important
    }

    .md-p-35 {
        padding: 3.5rem !important
    }

    .md-m-r-35 {
        margin-right: 3.5rem !important
    }

    .md-p-r-35 {
        padding-right: 3.5rem !important
    }
}

.m-35 {
    margin: 3.5rem
}

.p-35 {
    padding: 3.5rem
}

.m-b-35 {
    margin-bottom: 3.5rem
}

.p-b-35 {
    padding-bottom: 3.5rem
}

@media (max-width: 991px) {
    .lg-m-35 {
        margin: 3.5rem !important
    }

    .lg-p-35 {
        padding: 3.5rem !important
    }

    .lg-m-b-35 {
        margin-bottom: 3.5rem !important
    }

    .lg-p-b-35 {
        padding-bottom: 3.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-35 {
        margin: 3.5rem !important
    }

    .md-p-35 {
        padding: 3.5rem !important
    }

    .md-m-b-35 {
        margin-bottom: 3.5rem !important
    }

    .md-p-b-35 {
        padding-bottom: 3.5rem !important
    }
}

.m-35 {
    margin: 3.5rem
}

.p-35 {
    padding: 3.5rem
}

.m-l-35 {
    margin-left: 3.5rem
}

.p-l-35 {
    padding-left: 3.5rem
}

@media (max-width: 991px) {
    .lg-m-35 {
        margin: 3.5rem !important
    }

    .lg-p-35 {
        padding: 3.5rem !important
    }

    .lg-m-l-35 {
        margin-left: 3.5rem !important
    }

    .lg-p-l-35 {
        padding-left: 3.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-35 {
        margin: 3.5rem !important
    }

    .md-p-35 {
        padding: 3.5rem !important
    }

    .md-m-l-35 {
        margin-left: 3.5rem !important
    }

    .md-p-l-35 {
        padding-left: 3.5rem !important
    }
}

.m-40 {
    margin: 4rem
}

.p-40 {
    padding: 4rem
}

.m-t-40 {
    margin-top: 4rem
}

.p-t-40 {
    padding-top: 4rem
}

@media (max-width: 991px) {
    .lg-m-40 {
        margin: 4rem !important
    }

    .lg-p-40 {
        padding: 4rem !important
    }

    .lg-m-t-40 {
        margin-top: 4rem !important
    }

    .lg-p-t-40 {
        padding-top: 4rem !important
    }
}

@media (max-width: 767px) {
    .md-m-40 {
        margin: 4rem !important
    }

    .md-p-40 {
        padding: 4rem !important
    }

    .md-m-t-40 {
        margin-top: 4rem !important
    }

    .md-p-t-40 {
        padding-top: 4rem !important
    }
}

.m-40 {
    margin: 4rem
}

.p-40 {
    padding: 4rem
}

.m-r-40 {
    margin-right: 4rem
}

.p-r-40 {
    padding-right: 4rem
}

@media (max-width: 991px) {
    .lg-m-40 {
        margin: 4rem !important
    }

    .lg-p-40 {
        padding: 4rem !important
    }

    .lg-m-r-40 {
        margin-right: 4rem !important
    }

    .lg-p-r-40 {
        padding-right: 4rem !important
    }
}

@media (max-width: 767px) {
    .md-m-40 {
        margin: 4rem !important
    }

    .md-p-40 {
        padding: 4rem !important
    }

    .md-m-r-40 {
        margin-right: 4rem !important
    }

    .md-p-r-40 {
        padding-right: 4rem !important
    }
}

.m-40 {
    margin: 4rem
}

.p-40 {
    padding: 4rem
}

.m-b-40 {
    margin-bottom: 4rem
}

.p-b-40 {
    padding-bottom: 4rem
}

@media (max-width: 991px) {
    .lg-m-40 {
        margin: 4rem !important
    }

    .lg-p-40 {
        padding: 4rem !important
    }

    .lg-m-b-40 {
        margin-bottom: 4rem !important
    }

    .lg-p-b-40 {
        padding-bottom: 4rem !important
    }
}

@media (max-width: 767px) {
    .md-m-40 {
        margin: 4rem !important
    }

    .md-p-40 {
        padding: 4rem !important
    }

    .md-m-b-40 {
        margin-bottom: 4rem !important
    }

    .md-p-b-40 {
        padding-bottom: 4rem !important
    }
}

.m-40 {
    margin: 4rem
}

.p-40 {
    padding: 4rem
}

.m-l-40 {
    margin-left: 4rem
}

.p-l-40 {
    padding-left: 4rem
}

@media (max-width: 991px) {
    .lg-m-40 {
        margin: 4rem !important
    }

    .lg-p-40 {
        padding: 4rem !important
    }

    .lg-m-l-40 {
        margin-left: 4rem !important
    }

    .lg-p-l-40 {
        padding-left: 4rem !important
    }
}

@media (max-width: 767px) {
    .md-m-40 {
        margin: 4rem !important
    }

    .md-p-40 {
        padding: 4rem !important
    }

    .md-m-l-40 {
        margin-left: 4rem !important
    }

    .md-p-l-40 {
        padding-left: 4rem !important
    }
}

.m-45 {
    margin: 4.5rem
}

.p-45 {
    padding: 4.5rem
}

.m-t-45 {
    margin-top: 4.5rem
}

.p-t-45 {
    padding-top: 4.5rem
}

@media (max-width: 991px) {
    .lg-m-45 {
        margin: 4.5rem !important
    }

    .lg-p-45 {
        padding: 4.5rem !important
    }

    .lg-m-t-45 {
        margin-top: 4.5rem !important
    }

    .lg-p-t-45 {
        padding-top: 4.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-45 {
        margin: 4.5rem !important
    }

    .md-p-45 {
        padding: 4.5rem !important
    }

    .md-m-t-45 {
        margin-top: 4.5rem !important
    }

    .md-p-t-45 {
        padding-top: 4.5rem !important
    }
}

.m-45 {
    margin: 4.5rem
}

.p-45 {
    padding: 4.5rem
}

.m-r-45 {
    margin-right: 4.5rem
}

.p-r-45 {
    padding-right: 4.5rem
}

@media (max-width: 991px) {
    .lg-m-45 {
        margin: 4.5rem !important
    }

    .lg-p-45 {
        padding: 4.5rem !important
    }

    .lg-m-r-45 {
        margin-right: 4.5rem !important
    }

    .lg-p-r-45 {
        padding-right: 4.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-45 {
        margin: 4.5rem !important
    }

    .md-p-45 {
        padding: 4.5rem !important
    }

    .md-m-r-45 {
        margin-right: 4.5rem !important
    }

    .md-p-r-45 {
        padding-right: 4.5rem !important
    }
}

.m-45 {
    margin: 4.5rem
}

.p-45 {
    padding: 4.5rem
}

.m-b-45 {
    margin-bottom: 4.5rem
}

.p-b-45 {
    padding-bottom: 4.5rem
}

@media (max-width: 991px) {
    .lg-m-45 {
        margin: 4.5rem !important
    }

    .lg-p-45 {
        padding: 4.5rem !important
    }

    .lg-m-b-45 {
        margin-bottom: 4.5rem !important
    }

    .lg-p-b-45 {
        padding-bottom: 4.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-45 {
        margin: 4.5rem !important
    }

    .md-p-45 {
        padding: 4.5rem !important
    }

    .md-m-b-45 {
        margin-bottom: 4.5rem !important
    }

    .md-p-b-45 {
        padding-bottom: 4.5rem !important
    }
}

.m-45 {
    margin: 4.5rem
}

.p-45 {
    padding: 4.5rem
}

.m-l-45 {
    margin-left: 4.5rem
}

.p-l-45 {
    padding-left: 4.5rem
}

@media (max-width: 991px) {
    .lg-m-45 {
        margin: 4.5rem !important
    }

    .lg-p-45 {
        padding: 4.5rem !important
    }

    .lg-m-l-45 {
        margin-left: 4.5rem !important
    }

    .lg-p-l-45 {
        padding-left: 4.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-45 {
        margin: 4.5rem !important
    }

    .md-p-45 {
        padding: 4.5rem !important
    }

    .md-m-l-45 {
        margin-left: 4.5rem !important
    }

    .md-p-l-45 {
        padding-left: 4.5rem !important
    }
}

.m-50 {
    margin: 5rem
}

.p-50 {
    padding: 5rem
}

.m-t-50 {
    margin-top: 5rem
}

.p-t-50 {
    padding-top: 5rem
}

@media (max-width: 991px) {
    .lg-m-50 {
        margin: 5rem !important
    }

    .lg-p-50 {
        padding: 5rem !important
    }

    .lg-m-t-50 {
        margin-top: 5rem !important
    }

    .lg-p-t-50 {
        padding-top: 5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-50 {
        margin: 5rem !important
    }

    .md-p-50 {
        padding: 5rem !important
    }

    .md-m-t-50 {
        margin-top: 5rem !important
    }

    .md-p-t-50 {
        padding-top: 5rem !important
    }
}

.m-50 {
    margin: 5rem
}

.p-50 {
    padding: 5rem
}

.m-r-50 {
    margin-right: 5rem
}

.p-r-50 {
    padding-right: 5rem
}

@media (max-width: 991px) {
    .lg-m-50 {
        margin: 5rem !important
    }

    .lg-p-50 {
        padding: 5rem !important
    }

    .lg-m-r-50 {
        margin-right: 5rem !important
    }

    .lg-p-r-50 {
        padding-right: 5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-50 {
        margin: 5rem !important
    }

    .md-p-50 {
        padding: 5rem !important
    }

    .md-m-r-50 {
        margin-right: 5rem !important
    }

    .md-p-r-50 {
        padding-right: 5rem !important
    }
}

.m-50 {
    margin: 5rem
}

.p-50 {
    padding: 5rem
}

.m-b-50 {
    margin-bottom: 5rem
}

.p-b-50 {
    padding-bottom: 5rem
}

@media (max-width: 991px) {
    .lg-m-50 {
        margin: 5rem !important
    }

    .lg-p-50 {
        padding: 5rem !important
    }

    .lg-m-b-50 {
        margin-bottom: 5rem !important
    }

    .lg-p-b-50 {
        padding-bottom: 5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-50 {
        margin: 5rem !important
    }

    .md-p-50 {
        padding: 5rem !important
    }

    .md-m-b-50 {
        margin-bottom: 5rem !important
    }

    .md-p-b-50 {
        padding-bottom: 5rem !important
    }
}

.m-50 {
    margin: 5rem
}

.p-50 {
    padding: 5rem
}

.m-l-50 {
    margin-left: 5rem
}

.p-l-50 {
    padding-left: 5rem
}

@media (max-width: 991px) {
    .lg-m-50 {
        margin: 5rem !important
    }

    .lg-p-50 {
        padding: 5rem !important
    }

    .lg-m-l-50 {
        margin-left: 5rem !important
    }

    .lg-p-l-50 {
        padding-left: 5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-50 {
        margin: 5rem !important
    }

    .md-p-50 {
        padding: 5rem !important
    }

    .md-m-l-50 {
        margin-left: 5rem !important
    }

    .md-p-l-50 {
        padding-left: 5rem !important
    }
}

.m-55 {
    margin: 5.5rem
}

.p-55 {
    padding: 5.5rem
}

.m-t-55 {
    margin-top: 5.5rem
}

.p-t-55 {
    padding-top: 5.5rem
}

@media (max-width: 991px) {
    .lg-m-55 {
        margin: 5.5rem !important
    }

    .lg-p-55 {
        padding: 5.5rem !important
    }

    .lg-m-t-55 {
        margin-top: 5.5rem !important
    }

    .lg-p-t-55 {
        padding-top: 5.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-55 {
        margin: 5.5rem !important
    }

    .md-p-55 {
        padding: 5.5rem !important
    }

    .md-m-t-55 {
        margin-top: 5.5rem !important
    }

    .md-p-t-55 {
        padding-top: 5.5rem !important
    }
}

.m-55 {
    margin: 5.5rem
}

.p-55 {
    padding: 5.5rem
}

.m-r-55 {
    margin-right: 5.5rem
}

.p-r-55 {
    padding-right: 5.5rem
}

@media (max-width: 991px) {
    .lg-m-55 {
        margin: 5.5rem !important
    }

    .lg-p-55 {
        padding: 5.5rem !important
    }

    .lg-m-r-55 {
        margin-right: 5.5rem !important
    }

    .lg-p-r-55 {
        padding-right: 5.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-55 {
        margin: 5.5rem !important
    }

    .md-p-55 {
        padding: 5.5rem !important
    }

    .md-m-r-55 {
        margin-right: 5.5rem !important
    }

    .md-p-r-55 {
        padding-right: 5.5rem !important
    }
}

.m-55 {
    margin: 5.5rem
}

.p-55 {
    padding: 5.5rem
}

.m-b-55 {
    margin-bottom: 5.5rem
}

.p-b-55 {
    padding-bottom: 5.5rem
}

@media (max-width: 991px) {
    .lg-m-55 {
        margin: 5.5rem !important
    }

    .lg-p-55 {
        padding: 5.5rem !important
    }

    .lg-m-b-55 {
        margin-bottom: 5.5rem !important
    }

    .lg-p-b-55 {
        padding-bottom: 5.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-55 {
        margin: 5.5rem !important
    }

    .md-p-55 {
        padding: 5.5rem !important
    }

    .md-m-b-55 {
        margin-bottom: 5.5rem !important
    }

    .md-p-b-55 {
        padding-bottom: 5.5rem !important
    }
}

.m-55 {
    margin: 5.5rem
}

.p-55 {
    padding: 5.5rem
}

.m-l-55 {
    margin-left: 5.5rem
}

.p-l-55 {
    padding-left: 5.5rem
}

@media (max-width: 991px) {
    .lg-m-55 {
        margin: 5.5rem !important
    }

    .lg-p-55 {
        padding: 5.5rem !important
    }

    .lg-m-l-55 {
        margin-left: 5.5rem !important
    }

    .lg-p-l-55 {
        padding-left: 5.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-55 {
        margin: 5.5rem !important
    }

    .md-p-55 {
        padding: 5.5rem !important
    }

    .md-m-l-55 {
        margin-left: 5.5rem !important
    }

    .md-p-l-55 {
        padding-left: 5.5rem !important
    }
}

.m-60 {
    margin: 6rem
}

.p-60 {
    padding: 6rem
}

.m-t-60 {
    margin-top: 6rem
}

.p-t-60 {
    padding-top: 6rem
}

@media (max-width: 991px) {
    .lg-m-60 {
        margin: 6rem !important
    }

    .lg-p-60 {
        padding: 6rem !important
    }

    .lg-m-t-60 {
        margin-top: 6rem !important
    }

    .lg-p-t-60 {
        padding-top: 6rem !important
    }
}

@media (max-width: 767px) {
    .md-m-60 {
        margin: 6rem !important
    }

    .md-p-60 {
        padding: 6rem !important
    }

    .md-m-t-60 {
        margin-top: 6rem !important
    }

    .md-p-t-60 {
        padding-top: 6rem !important
    }
}

.m-60 {
    margin: 6rem
}

.p-60 {
    padding: 6rem
}

.m-r-60 {
    margin-right: 6rem
}

.p-r-60 {
    padding-right: 6rem
}

@media (max-width: 991px) {
    .lg-m-60 {
        margin: 6rem !important
    }

    .lg-p-60 {
        padding: 6rem !important
    }

    .lg-m-r-60 {
        margin-right: 6rem !important
    }

    .lg-p-r-60 {
        padding-right: 6rem !important
    }
}

@media (max-width: 767px) {
    .md-m-60 {
        margin: 6rem !important
    }

    .md-p-60 {
        padding: 6rem !important
    }

    .md-m-r-60 {
        margin-right: 6rem !important
    }

    .md-p-r-60 {
        padding-right: 6rem !important
    }
}

.m-60 {
    margin: 6rem
}

.p-60 {
    padding: 6rem
}

.m-b-60 {
    margin-bottom: 6rem
}

.p-b-60 {
    padding-bottom: 6rem
}

@media (max-width: 991px) {
    .lg-m-60 {
        margin: 6rem !important
    }

    .lg-p-60 {
        padding: 6rem !important
    }

    .lg-m-b-60 {
        margin-bottom: 6rem !important
    }

    .lg-p-b-60 {
        padding-bottom: 6rem !important
    }
}

@media (max-width: 767px) {
    .md-m-60 {
        margin: 6rem !important
    }

    .md-p-60 {
        padding: 6rem !important
    }

    .md-m-b-60 {
        margin-bottom: 6rem !important
    }

    .md-p-b-60 {
        padding-bottom: 6rem !important
    }
}

.m-60 {
    margin: 6rem
}

.p-60 {
    padding: 6rem
}

.m-l-60 {
    margin-left: 6rem
}

.p-l-60 {
    padding-left: 6rem
}

@media (max-width: 991px) {
    .lg-m-60 {
        margin: 6rem !important
    }

    .lg-p-60 {
        padding: 6rem !important
    }

    .lg-m-l-60 {
        margin-left: 6rem !important
    }

    .lg-p-l-60 {
        padding-left: 6rem !important
    }
}

@media (max-width: 767px) {
    .md-m-60 {
        margin: 6rem !important
    }

    .md-p-60 {
        padding: 6rem !important
    }

    .md-m-l-60 {
        margin-left: 6rem !important
    }

    .md-p-l-60 {
        padding-left: 6rem !important
    }
}

.m-65 {
    margin: 6.5rem
}

.p-65 {
    padding: 6.5rem
}

.m-t-65 {
    margin-top: 6.5rem
}

.p-t-65 {
    padding-top: 6.5rem
}

@media (max-width: 991px) {
    .lg-m-65 {
        margin: 6.5rem !important
    }

    .lg-p-65 {
        padding: 6.5rem !important
    }

    .lg-m-t-65 {
        margin-top: 6.5rem !important
    }

    .lg-p-t-65 {
        padding-top: 6.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-65 {
        margin: 6.5rem !important
    }

    .md-p-65 {
        padding: 6.5rem !important
    }

    .md-m-t-65 {
        margin-top: 6.5rem !important
    }

    .md-p-t-65 {
        padding-top: 6.5rem !important
    }
}

.m-65 {
    margin: 6.5rem
}

.p-65 {
    padding: 6.5rem
}

.m-r-65 {
    margin-right: 6.5rem
}

.p-r-65 {
    padding-right: 6.5rem
}

@media (max-width: 991px) {
    .lg-m-65 {
        margin: 6.5rem !important
    }

    .lg-p-65 {
        padding: 6.5rem !important
    }

    .lg-m-r-65 {
        margin-right: 6.5rem !important
    }

    .lg-p-r-65 {
        padding-right: 6.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-65 {
        margin: 6.5rem !important
    }

    .md-p-65 {
        padding: 6.5rem !important
    }

    .md-m-r-65 {
        margin-right: 6.5rem !important
    }

    .md-p-r-65 {
        padding-right: 6.5rem !important
    }
}

.m-65 {
    margin: 6.5rem
}

.p-65 {
    padding: 6.5rem
}

.m-b-65 {
    margin-bottom: 6.5rem
}

.p-b-65 {
    padding-bottom: 6.5rem
}

@media (max-width: 991px) {
    .lg-m-65 {
        margin: 6.5rem !important
    }

    .lg-p-65 {
        padding: 6.5rem !important
    }

    .lg-m-b-65 {
        margin-bottom: 6.5rem !important
    }

    .lg-p-b-65 {
        padding-bottom: 6.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-65 {
        margin: 6.5rem !important
    }

    .md-p-65 {
        padding: 6.5rem !important
    }

    .md-m-b-65 {
        margin-bottom: 6.5rem !important
    }

    .md-p-b-65 {
        padding-bottom: 6.5rem !important
    }
}

.m-65 {
    margin: 6.5rem
}

.p-65 {
    padding: 6.5rem
}

.m-l-65 {
    margin-left: 6.5rem
}

.p-l-65 {
    padding-left: 6.5rem
}

@media (max-width: 991px) {
    .lg-m-65 {
        margin: 6.5rem !important
    }

    .lg-p-65 {
        padding: 6.5rem !important
    }

    .lg-m-l-65 {
        margin-left: 6.5rem !important
    }

    .lg-p-l-65 {
        padding-left: 6.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-65 {
        margin: 6.5rem !important
    }

    .md-p-65 {
        padding: 6.5rem !important
    }

    .md-m-l-65 {
        margin-left: 6.5rem !important
    }

    .md-p-l-65 {
        padding-left: 6.5rem !important
    }
}

.m-70 {
    margin: 7rem
}

.p-70 {
    padding: 7rem
}

.m-t-70 {
    margin-top: 7rem
}

.p-t-70 {
    padding-top: 7rem
}

@media (max-width: 991px) {
    .lg-m-70 {
        margin: 7rem !important
    }

    .lg-p-70 {
        padding: 7rem !important
    }

    .lg-m-t-70 {
        margin-top: 7rem !important
    }

    .lg-p-t-70 {
        padding-top: 7rem !important
    }
}

@media (max-width: 767px) {
    .md-m-70 {
        margin: 7rem !important
    }

    .md-p-70 {
        padding: 7rem !important
    }

    .md-m-t-70 {
        margin-top: 7rem !important
    }

    .md-p-t-70 {
        padding-top: 7rem !important
    }
}

.m-70 {
    margin: 7rem
}

.p-70 {
    padding: 7rem
}

.m-r-70 {
    margin-right: 7rem
}

.p-r-70 {
    padding-right: 7rem
}

@media (max-width: 991px) {
    .lg-m-70 {
        margin: 7rem !important
    }

    .lg-p-70 {
        padding: 7rem !important
    }

    .lg-m-r-70 {
        margin-right: 7rem !important
    }

    .lg-p-r-70 {
        padding-right: 7rem !important
    }
}

@media (max-width: 767px) {
    .md-m-70 {
        margin: 7rem !important
    }

    .md-p-70 {
        padding: 7rem !important
    }

    .md-m-r-70 {
        margin-right: 7rem !important
    }

    .md-p-r-70 {
        padding-right: 7rem !important
    }
}

.m-70 {
    margin: 7rem
}

.p-70 {
    padding: 7rem
}

.m-b-70 {
    margin-bottom: 7rem
}

.p-b-70 {
    padding-bottom: 7rem
}

@media (max-width: 991px) {
    .lg-m-70 {
        margin: 7rem !important
    }

    .lg-p-70 {
        padding: 7rem !important
    }

    .lg-m-b-70 {
        margin-bottom: 7rem !important
    }

    .lg-p-b-70 {
        padding-bottom: 7rem !important
    }
}

@media (max-width: 767px) {
    .md-m-70 {
        margin: 7rem !important
    }

    .md-p-70 {
        padding: 7rem !important
    }

    .md-m-b-70 {
        margin-bottom: 7rem !important
    }

    .md-p-b-70 {
        padding-bottom: 7rem !important
    }
}

.m-70 {
    margin: 7rem
}

.p-70 {
    padding: 7rem
}

.m-l-70 {
    margin-left: 7rem
}

.p-l-70 {
    padding-left: 7rem
}

@media (max-width: 991px) {
    .lg-m-70 {
        margin: 7rem !important
    }

    .lg-p-70 {
        padding: 7rem !important
    }

    .lg-m-l-70 {
        margin-left: 7rem !important
    }

    .lg-p-l-70 {
        padding-left: 7rem !important
    }
}

@media (max-width: 767px) {
    .md-m-70 {
        margin: 7rem !important
    }

    .md-p-70 {
        padding: 7rem !important
    }

    .md-m-l-70 {
        margin-left: 7rem !important
    }

    .md-p-l-70 {
        padding-left: 7rem !important
    }
}

.m-75 {
    margin: 7.5rem
}

.p-75 {
    padding: 7.5rem
}

.m-t-75 {
    margin-top: 7.5rem
}

.p-t-75 {
    padding-top: 7.5rem
}

@media (max-width: 991px) {
    .lg-m-75 {
        margin: 7.5rem !important
    }

    .lg-p-75 {
        padding: 7.5rem !important
    }

    .lg-m-t-75 {
        margin-top: 7.5rem !important
    }

    .lg-p-t-75 {
        padding-top: 7.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-75 {
        margin: 7.5rem !important
    }

    .md-p-75 {
        padding: 7.5rem !important
    }

    .md-m-t-75 {
        margin-top: 7.5rem !important
    }

    .md-p-t-75 {
        padding-top: 7.5rem !important
    }
}

.m-75 {
    margin: 7.5rem
}

.p-75 {
    padding: 7.5rem
}

.m-r-75 {
    margin-right: 7.5rem
}

.p-r-75 {
    padding-right: 7.5rem
}

@media (max-width: 991px) {
    .lg-m-75 {
        margin: 7.5rem !important
    }

    .lg-p-75 {
        padding: 7.5rem !important
    }

    .lg-m-r-75 {
        margin-right: 7.5rem !important
    }

    .lg-p-r-75 {
        padding-right: 7.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-75 {
        margin: 7.5rem !important
    }

    .md-p-75 {
        padding: 7.5rem !important
    }

    .md-m-r-75 {
        margin-right: 7.5rem !important
    }

    .md-p-r-75 {
        padding-right: 7.5rem !important
    }
}

.m-75 {
    margin: 7.5rem
}

.p-75 {
    padding: 7.5rem
}

.m-b-75 {
    margin-bottom: 7.5rem
}

.p-b-75 {
    padding-bottom: 7.5rem
}

@media (max-width: 991px) {
    .lg-m-75 {
        margin: 7.5rem !important
    }

    .lg-p-75 {
        padding: 7.5rem !important
    }

    .lg-m-b-75 {
        margin-bottom: 7.5rem !important
    }

    .lg-p-b-75 {
        padding-bottom: 7.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-75 {
        margin: 7.5rem !important
    }

    .md-p-75 {
        padding: 7.5rem !important
    }

    .md-m-b-75 {
        margin-bottom: 7.5rem !important
    }

    .md-p-b-75 {
        padding-bottom: 7.5rem !important
    }
}

.m-75 {
    margin: 7.5rem
}

.p-75 {
    padding: 7.5rem
}

.m-l-75 {
    margin-left: 7.5rem
}

.p-l-75 {
    padding-left: 7.5rem
}

@media (max-width: 991px) {
    .lg-m-75 {
        margin: 7.5rem !important
    }

    .lg-p-75 {
        padding: 7.5rem !important
    }

    .lg-m-l-75 {
        margin-left: 7.5rem !important
    }

    .lg-p-l-75 {
        padding-left: 7.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-75 {
        margin: 7.5rem !important
    }

    .md-p-75 {
        padding: 7.5rem !important
    }

    .md-m-l-75 {
        margin-left: 7.5rem !important
    }

    .md-p-l-75 {
        padding-left: 7.5rem !important
    }
}

.m-80 {
    margin: 8rem
}

.p-80 {
    padding: 8rem
}

.m-t-80 {
    margin-top: 8rem
}

.p-t-80 {
    padding-top: 8rem
}

@media (max-width: 991px) {
    .lg-m-80 {
        margin: 8rem !important
    }

    .lg-p-80 {
        padding: 8rem !important
    }

    .lg-m-t-80 {
        margin-top: 8rem !important
    }

    .lg-p-t-80 {
        padding-top: 8rem !important
    }
}

@media (max-width: 767px) {
    .md-m-80 {
        margin: 8rem !important
    }

    .md-p-80 {
        padding: 8rem !important
    }

    .md-m-t-80 {
        margin-top: 8rem !important
    }

    .md-p-t-80 {
        padding-top: 8rem !important
    }
}

.m-80 {
    margin: 8rem
}

.p-80 {
    padding: 8rem
}

.m-r-80 {
    margin-right: 8rem
}

.p-r-80 {
    padding-right: 8rem
}

@media (max-width: 991px) {
    .lg-m-80 {
        margin: 8rem !important
    }

    .lg-p-80 {
        padding: 8rem !important
    }

    .lg-m-r-80 {
        margin-right: 8rem !important
    }

    .lg-p-r-80 {
        padding-right: 8rem !important
    }
}

@media (max-width: 767px) {
    .md-m-80 {
        margin: 8rem !important
    }

    .md-p-80 {
        padding: 8rem !important
    }

    .md-m-r-80 {
        margin-right: 8rem !important
    }

    .md-p-r-80 {
        padding-right: 8rem !important
    }
}

.m-80 {
    margin: 8rem
}

.p-80 {
    padding: 8rem
}

.m-b-80 {
    margin-bottom: 8rem
}

.p-b-80 {
    padding-bottom: 8rem
}

@media (max-width: 991px) {
    .lg-m-80 {
        margin: 8rem !important
    }

    .lg-p-80 {
        padding: 8rem !important
    }

    .lg-m-b-80 {
        margin-bottom: 8rem !important
    }

    .lg-p-b-80 {
        padding-bottom: 8rem !important
    }
}

@media (max-width: 767px) {
    .md-m-80 {
        margin: 8rem !important
    }

    .md-p-80 {
        padding: 8rem !important
    }

    .md-m-b-80 {
        margin-bottom: 8rem !important
    }

    .md-p-b-80 {
        padding-bottom: 8rem !important
    }
}

.m-80 {
    margin: 8rem
}

.p-80 {
    padding: 8rem
}

.m-l-80 {
    margin-left: 8rem
}

.p-l-80 {
    padding-left: 8rem
}

@media (max-width: 991px) {
    .lg-m-80 {
        margin: 8rem !important
    }

    .lg-p-80 {
        padding: 8rem !important
    }

    .lg-m-l-80 {
        margin-left: 8rem !important
    }

    .lg-p-l-80 {
        padding-left: 8rem !important
    }
}

@media (max-width: 767px) {
    .md-m-80 {
        margin: 8rem !important
    }

    .md-p-80 {
        padding: 8rem !important
    }

    .md-m-l-80 {
        margin-left: 8rem !important
    }

    .md-p-l-80 {
        padding-left: 8rem !important
    }
}

.m-85 {
    margin: 8.5rem
}

.p-85 {
    padding: 8.5rem
}

.m-t-85 {
    margin-top: 8.5rem
}

.p-t-85 {
    padding-top: 8.5rem
}

@media (max-width: 991px) {
    .lg-m-85 {
        margin: 8.5rem !important
    }

    .lg-p-85 {
        padding: 8.5rem !important
    }

    .lg-m-t-85 {
        margin-top: 8.5rem !important
    }

    .lg-p-t-85 {
        padding-top: 8.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-85 {
        margin: 8.5rem !important
    }

    .md-p-85 {
        padding: 8.5rem !important
    }

    .md-m-t-85 {
        margin-top: 8.5rem !important
    }

    .md-p-t-85 {
        padding-top: 8.5rem !important
    }
}

.m-85 {
    margin: 8.5rem
}

.p-85 {
    padding: 8.5rem
}

.m-r-85 {
    margin-right: 8.5rem
}

.p-r-85 {
    padding-right: 8.5rem
}

@media (max-width: 991px) {
    .lg-m-85 {
        margin: 8.5rem !important
    }

    .lg-p-85 {
        padding: 8.5rem !important
    }

    .lg-m-r-85 {
        margin-right: 8.5rem !important
    }

    .lg-p-r-85 {
        padding-right: 8.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-85 {
        margin: 8.5rem !important
    }

    .md-p-85 {
        padding: 8.5rem !important
    }

    .md-m-r-85 {
        margin-right: 8.5rem !important
    }

    .md-p-r-85 {
        padding-right: 8.5rem !important
    }
}

.m-85 {
    margin: 8.5rem
}

.p-85 {
    padding: 8.5rem
}

.m-b-85 {
    margin-bottom: 8.5rem
}

.p-b-85 {
    padding-bottom: 8.5rem
}

@media (max-width: 991px) {
    .lg-m-85 {
        margin: 8.5rem !important
    }

    .lg-p-85 {
        padding: 8.5rem !important
    }

    .lg-m-b-85 {
        margin-bottom: 8.5rem !important
    }

    .lg-p-b-85 {
        padding-bottom: 8.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-85 {
        margin: 8.5rem !important
    }

    .md-p-85 {
        padding: 8.5rem !important
    }

    .md-m-b-85 {
        margin-bottom: 8.5rem !important
    }

    .md-p-b-85 {
        padding-bottom: 8.5rem !important
    }
}

.m-85 {
    margin: 8.5rem
}

.p-85 {
    padding: 8.5rem
}

.m-l-85 {
    margin-left: 8.5rem
}

.p-l-85 {
    padding-left: 8.5rem
}

@media (max-width: 991px) {
    .lg-m-85 {
        margin: 8.5rem !important
    }

    .lg-p-85 {
        padding: 8.5rem !important
    }

    .lg-m-l-85 {
        margin-left: 8.5rem !important
    }

    .lg-p-l-85 {
        padding-left: 8.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-85 {
        margin: 8.5rem !important
    }

    .md-p-85 {
        padding: 8.5rem !important
    }

    .md-m-l-85 {
        margin-left: 8.5rem !important
    }

    .md-p-l-85 {
        padding-left: 8.5rem !important
    }
}

.m-90 {
    margin: 9rem
}

.p-90 {
    padding: 9rem
}

.m-t-90 {
    margin-top: 9rem
}

.p-t-90 {
    padding-top: 9rem
}

@media (max-width: 991px) {
    .lg-m-90 {
        margin: 9rem !important
    }

    .lg-p-90 {
        padding: 9rem !important
    }

    .lg-m-t-90 {
        margin-top: 9rem !important
    }

    .lg-p-t-90 {
        padding-top: 9rem !important
    }
}

@media (max-width: 767px) {
    .md-m-90 {
        margin: 9rem !important
    }

    .md-p-90 {
        padding: 9rem !important
    }

    .md-m-t-90 {
        margin-top: 9rem !important
    }

    .md-p-t-90 {
        padding-top: 9rem !important
    }
}

.m-90 {
    margin: 9rem
}

.p-90 {
    padding: 9rem
}

.m-r-90 {
    margin-right: 9rem
}

.p-r-90 {
    padding-right: 9rem
}

@media (max-width: 991px) {
    .lg-m-90 {
        margin: 9rem !important
    }

    .lg-p-90 {
        padding: 9rem !important
    }

    .lg-m-r-90 {
        margin-right: 9rem !important
    }

    .lg-p-r-90 {
        padding-right: 9rem !important
    }
}

@media (max-width: 767px) {
    .md-m-90 {
        margin: 9rem !important
    }

    .md-p-90 {
        padding: 9rem !important
    }

    .md-m-r-90 {
        margin-right: 9rem !important
    }

    .md-p-r-90 {
        padding-right: 9rem !important
    }
}

.m-90 {
    margin: 9rem
}

.p-90 {
    padding: 9rem
}

.m-b-90 {
    margin-bottom: 9rem
}

.p-b-90 {
    padding-bottom: 9rem
}

@media (max-width: 991px) {
    .lg-m-90 {
        margin: 9rem !important
    }

    .lg-p-90 {
        padding: 9rem !important
    }

    .lg-m-b-90 {
        margin-bottom: 9rem !important
    }

    .lg-p-b-90 {
        padding-bottom: 9rem !important
    }
}

@media (max-width: 767px) {
    .md-m-90 {
        margin: 9rem !important
    }

    .md-p-90 {
        padding: 9rem !important
    }

    .md-m-b-90 {
        margin-bottom: 9rem !important
    }

    .md-p-b-90 {
        padding-bottom: 9rem !important
    }
}

.m-90 {
    margin: 9rem
}

.p-90 {
    padding: 9rem
}

.m-l-90 {
    margin-left: 9rem
}

.p-l-90 {
    padding-left: 9rem
}

@media (max-width: 991px) {
    .lg-m-90 {
        margin: 9rem !important
    }

    .lg-p-90 {
        padding: 9rem !important
    }

    .lg-m-l-90 {
        margin-left: 9rem !important
    }

    .lg-p-l-90 {
        padding-left: 9rem !important
    }
}

@media (max-width: 767px) {
    .md-m-90 {
        margin: 9rem !important
    }

    .md-p-90 {
        padding: 9rem !important
    }

    .md-m-l-90 {
        margin-left: 9rem !important
    }

    .md-p-l-90 {
        padding-left: 9rem !important
    }
}

.m-95 {
    margin: 9.5rem
}

.p-95 {
    padding: 9.5rem
}

.m-t-95 {
    margin-top: 9.5rem
}

.p-t-95 {
    padding-top: 9.5rem
}

@media (max-width: 991px) {
    .lg-m-95 {
        margin: 9.5rem !important
    }

    .lg-p-95 {
        padding: 9.5rem !important
    }

    .lg-m-t-95 {
        margin-top: 9.5rem !important
    }

    .lg-p-t-95 {
        padding-top: 9.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-95 {
        margin: 9.5rem !important
    }

    .md-p-95 {
        padding: 9.5rem !important
    }

    .md-m-t-95 {
        margin-top: 9.5rem !important
    }

    .md-p-t-95 {
        padding-top: 9.5rem !important
    }
}

.m-95 {
    margin: 9.5rem
}

.p-95 {
    padding: 9.5rem
}

.m-r-95 {
    margin-right: 9.5rem
}

.p-r-95 {
    padding-right: 9.5rem
}

@media (max-width: 991px) {
    .lg-m-95 {
        margin: 9.5rem !important
    }

    .lg-p-95 {
        padding: 9.5rem !important
    }

    .lg-m-r-95 {
        margin-right: 9.5rem !important
    }

    .lg-p-r-95 {
        padding-right: 9.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-95 {
        margin: 9.5rem !important
    }

    .md-p-95 {
        padding: 9.5rem !important
    }

    .md-m-r-95 {
        margin-right: 9.5rem !important
    }

    .md-p-r-95 {
        padding-right: 9.5rem !important
    }
}

.m-95 {
    margin: 9.5rem
}

.p-95 {
    padding: 9.5rem
}

.m-b-95 {
    margin-bottom: 9.5rem
}

.p-b-95 {
    padding-bottom: 9.5rem
}

@media (max-width: 991px) {
    .lg-m-95 {
        margin: 9.5rem !important
    }

    .lg-p-95 {
        padding: 9.5rem !important
    }

    .lg-m-b-95 {
        margin-bottom: 9.5rem !important
    }

    .lg-p-b-95 {
        padding-bottom: 9.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-95 {
        margin: 9.5rem !important
    }

    .md-p-95 {
        padding: 9.5rem !important
    }

    .md-m-b-95 {
        margin-bottom: 9.5rem !important
    }

    .md-p-b-95 {
        padding-bottom: 9.5rem !important
    }
}

.m-95 {
    margin: 9.5rem
}

.p-95 {
    padding: 9.5rem
}

.m-l-95 {
    margin-left: 9.5rem
}

.p-l-95 {
    padding-left: 9.5rem
}

@media (max-width: 991px) {
    .lg-m-95 {
        margin: 9.5rem !important
    }

    .lg-p-95 {
        padding: 9.5rem !important
    }

    .lg-m-l-95 {
        margin-left: 9.5rem !important
    }

    .lg-p-l-95 {
        padding-left: 9.5rem !important
    }
}

@media (max-width: 767px) {
    .md-m-95 {
        margin: 9.5rem !important
    }

    .md-p-95 {
        padding: 9.5rem !important
    }

    .md-m-l-95 {
        margin-left: 9.5rem !important
    }

    .md-p-l-95 {
        padding-left: 9.5rem !important
    }
}

.m-100 {
    margin: 10rem
}

.p-100 {
    padding: 10rem
}

.m-t-100 {
    margin-top: 10rem
}

.p-t-100 {
    padding-top: 10rem
}

@media (max-width: 991px) {
    .lg-m-100 {
        margin: 10rem !important
    }

    .lg-p-100 {
        padding: 10rem !important
    }

    .lg-m-t-100 {
        margin-top: 10rem !important
    }

    .lg-p-t-100 {
        padding-top: 10rem !important
    }
}

@media (max-width: 767px) {
    .md-m-100 {
        margin: 10rem !important
    }

    .md-p-100 {
        padding: 10rem !important
    }

    .md-m-t-100 {
        margin-top: 10rem !important
    }

    .md-p-t-100 {
        padding-top: 10rem !important
    }
}

.m-100 {
    margin: 10rem
}

.p-100 {
    padding: 10rem
}

.m-r-100 {
    margin-right: 10rem
}

.p-r-100 {
    padding-right: 10rem
}

@media (max-width: 991px) {
    .lg-m-100 {
        margin: 10rem !important
    }

    .lg-p-100 {
        padding: 10rem !important
    }

    .lg-m-r-100 {
        margin-right: 10rem !important
    }

    .lg-p-r-100 {
        padding-right: 10rem !important
    }
}

@media (max-width: 767px) {
    .md-m-100 {
        margin: 10rem !important
    }

    .md-p-100 {
        padding: 10rem !important
    }

    .md-m-r-100 {
        margin-right: 10rem !important
    }

    .md-p-r-100 {
        padding-right: 10rem !important
    }
}

.m-100 {
    margin: 10rem
}

.p-100 {
    padding: 10rem
}

.m-b-100 {
    margin-bottom: 10rem
}

.p-b-100 {
    padding-bottom: 10rem
}

@media (max-width: 991px) {
    .lg-m-100 {
        margin: 10rem !important
    }

    .lg-p-100 {
        padding: 10rem !important
    }

    .lg-m-b-100 {
        margin-bottom: 10rem !important
    }

    .lg-p-b-100 {
        padding-bottom: 10rem !important
    }
}

@media (max-width: 767px) {
    .md-m-100 {
        margin: 10rem !important
    }

    .md-p-100 {
        padding: 10rem !important
    }

    .md-m-b-100 {
        margin-bottom: 10rem !important
    }

    .md-p-b-100 {
        padding-bottom: 10rem !important
    }
}

.m-100 {
    margin: 10rem
}

.p-100 {
    padding: 10rem
}

.m-l-100 {
    margin-left: 10rem
}

.p-l-100 {
    padding-left: 10rem
}

@media (max-width: 991px) {
    .lg-m-100 {
        margin: 10rem !important
    }

    .lg-p-100 {
        padding: 10rem !important
    }

    .lg-m-l-100 {
        margin-left: 10rem !important
    }

    .lg-p-l-100 {
        padding-left: 10rem !important
    }
}

@media (max-width: 767px) {
    .md-m-100 {
        margin: 10rem !important
    }

    .md-p-100 {
        padding: 10rem !important
    }

    .md-m-l-100 {
        margin-left: 10rem !important
    }

    .md-p-l-100 {
        padding-left: 10rem !important
    }
}

html {
    font-size: 10px;
    margin: 0;
    padding: 0;
    overflow-x: hidden
}

@media (max-width: 1199px) {
    html {
        font-size: 9px
    }
}

@media (max-width: 991px) {
    html {
        font-size: 8px
    }
}

body {
    position: relative;
    font-family: var(--primary-font);
    font-size: 16px;
    line-height: 1.7;
    margin: 0;
    padding: 0;
    color: var(--typo-body)
}

::selection {
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    text-shadow: none
}

hr {
    display: block;
    height: 1px;
    border: 0;
    margin: 0;
    padding: 0
}

audio,
canvas,
iframe,
img,
svg,
video {
    vertical-align: middle
}

fieldset {
    border: 0;
    margin: 0;
    padding: 0
}

textarea {
    resize: vertical
}

a,
button {
    outline: none;
    -webkit-transition: all .5s;
    transition: all .5s
}

a {
    color: var(--action-color)
}

a:hover {
    color: var(--action-color);
    text-decoration: underline
}

* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.container {
    width: 1260px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px
}

@media (max-width: 1199px) {
    .container {
        width: 960px
    }
}

@media (max-width: 991px) {
    .container {
        width: 650px
    }
}

@media (max-width: 767px) {
    .container {
        width: 100%
    }
}

.container-fluid {
    padding: 0 4rem
}

@media (max-width: 767px) {
    .container-fluid {
        padding: 0 2rem
    }
}

figure {
    margin: 0;
    padding: 0
}

@media (max-width: 991px) {
    figure img {
        width: 100%;
        height: auto
    }
}

@media (max-width: 991px) {
    .section-gap {
        padding-top: 10rem;
        padding-bottom: 10rem
    }
}

.object-fit__cover {
    -o-object-fit: cover;
    object-fit: cover
}

.object-fit__contain {
    -o-object-fit: contain;
    object-fit: contain
}

.visuallyhidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    white-space: nowrap
}

.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
    clip: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    position: static;
    width: auto;
    white-space: inherit
}

.invisible {
    visibility: hidden
}

.border-radius {
    border-radius: var(--radius)
}

.no-overflow {
    overflow: hidden
}

.bottom-border {
    border-bottom: 2px solid var(--bg-light)
}

.primary-font {
    font-family: var(--primary-font)
}

.primary-color {
    color: var(--primary-color)
}

.typo-dark {
    color: var(--typo-dark)
}

.typo-body {
    color: var(--typo-body)
}

.typo-mid {
    color: var(--typo-mid)
}

.typo-light {
    color: var(--typo-light)
}

.bg-ex-light {
    color: var(--bg-ex-light)
}

.color-success {
    color: var(--color-success)
}

.color-alert {
    color: var(--color-alert)
}

.color-info {
    color: var(--color-info)
}

.color-error {
    color: var(--color-error)
}

.color-mixed-grey {
    color: var(--mixed-grey)
}

.color-white {
    color: #fff
}

.color-hilight {
    color: var(--color-hilight)
}

.primary-bg-color {
    background-color: var(--primary-color)
}

.typo-bg-dark {
    background-color: var(--typo-dark)
}

.typo-bg-body {
    background-color: var(--typo-body)
}

.typo-bg-mid {
    background-color: var(--typo-mid)
}

.typo-bg-light {
    background-color: var(--typo-light)
}

.bg-light {
    background-color: var(--bg-light)
}

.bg-ex-light {
    background-color: var(--bg-ex-light)
}

.color-bg-success {
    background-color: var(--color-success)
}

.color-bg-alert {
    background-color: var(--color-alert)
}

.color-bg-info {
    background-color: var(--color-info)
}

.color-bg-error {
    background-color: var(--color-error)
}

.bg-white {
    background-color: #fff
}

.radius {
    border-radius: var(--radius)
}

.radius-big {
    border-radius: var(--radius-big)
}

.grad-bg {
    background: -webkit-gradient(linear, left bottom, left top, from(#EAEDF3), to(#fff));
    background: linear-gradient(0deg, #EAEDF3 0%, #fff 100%)
}

.shadow-p1 {
    -webkit-box-shadow: 0 1px 1px 0 rgba(10, 31, 68, 0.08);
    box-shadow: 0 1px 1px 0 rgba(10, 31, 68, 0.08)
}

.shadow-p2 {
    -webkit-box-shadow: 0 3px 6px 0 rgba(10, 31, 68, 0.1);
    box-shadow: 0 3px 6px 0 rgba(10, 31, 68, 0.1)
}

.section-subtitle {
    font-size: 2rem;
    font-weight: var(--w-semi-bold)
}

.brand-logo {
    display: block
}

@media print {

    *,
    *:before,
    *:after {
        background: transparent !important;
        -webkit-box-shadow: none !important;
        box-shadow: none !important;
        text-shadow: none !important
    }

    a,
    a:visited {
        text-decoration: underline
    }

    a[href]:after {
        content: " (" attr(href) ")"
    }

    abbr[title]:after {
        content: " (" attr(title) ")"
    }

    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: ""
    }

    pre {
        white-space: pre-wrap !important
    }

    pre,
    blockquote {
        page-break-inside: avoid
    }

    thead {
        display: table-header-group
    }

    tr,
    img {
        page-break-inside: avoid
    }

    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }

    h2,
    h3 {
        page-break-after: avoid
    }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--secondary-font);
    color: var(--typo-dark);
    font-weight: var(--w-bold);
    line-height: 1.5
}

h1 {
    font-size: 4.4rem
}

@media (max-width: 991px) {
    h1 {
        font-size: 3.2rem
    }
}

h2 {
    font-size: 3.6rem
}

@media (max-width: 991px) {
    h2 {
        font-size: 3rem
    }
}

h3 {
    font-size: 2.4rem
}

h4 {
    font-size: 2rem;
    font-weight: var(--w-bold)
}

h4.sml {
    font-size: 2.2rem
}

h5 {
    font-size: 1.8rem;
    font-weight: var(--w-bold)
}

h6 {
    font-size: 1.6rem;
    font-weight: var(--w-semi-bold)
}

p {
    color: var(--typo-body)
}

@media (max-width: 991px) {
    p {
        font-size: 16px
    }
}

.w-light {
    font-weight: var(--w-light)
}

.w-regular {
    font-weight: var(--w-regular)
}

.w-semi-bold {
    font-weight: var(--w-semi-bold)
}

.w-bold {
    font-weight: var(--w-bold)
}

.w-ex-bold {
    font-weight: var(--w-extra-bold)
}

.main h3 {
    color: #f84e1d;
}

.page-header {
    background-color: #f84e1d;
    height: 8rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%
}

.page-header h3 {
    color: #FFFFFF;
    font-size: 34px;
}

@media (max-width: 767px) {
    .page-header {
        height: auto
    }
}

.page-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

@media (max-width: 767px) {
    .page-nav {
        display: block
    }

    .page-nav .nav-brand {
        display: block;
        margin: 20px 0 20px;
        text-align: center
    }
}

.nav-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.nav-list a {
    font-size: 14px;
    font-weight: var(--w-medium);
    color: var(--typo-body);
    text-transform: uppercase;
    text-decoration: none;
    margin-left: 3rem
}

.nav-list a:nth-of-type(1) {
    margin-left: 0
}

.nav-list a:hover {
    color: var(--primary-color)
}

.nav-list .toggle-navbar {
    font-size: 20px;
    line-height: 1;
    display: none
}

@media (max-width: 1199px) {
    .nav-list .toggle-navbar {
        display: block
    }
}

@media (max-width: 767px) {
    .nav-list {
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin: 10px 0 15px
    }

    .nav-list a {
        font-size: 12px;
        margin-left: 2rem
    }
}

.main-content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.sidebar-navigation {
    position: fixed;
    top: 120px;
    bottom: 40px;
    width: 350px;
    padding: 3rem;
    background-color: #1e1e1e;
    overflow-x: hidden;
    -webkit-transition: all .5s;
    transition: all .5s
        /* Track */


        /* Handle */


        /* Handle on hover */

}

.sidebar-navigation::-webkit-scrollbar {
    width: 5px;
}

.sidebar-navigation::-webkit-scrollbar-track {
    background: #ffffff;
}

.sidebar-navigation::-webkit-scrollbar-thumb {
    background: var(--action-color);
}

.sidebar-navigation::-webkit-scrollbar-thumb:hover {
    background: #ffffff;
}

.sidebar-navigation.stick-to-top {
    top: 40px
}



@media (max-width: 1199px) {
    .sidebar-navigation.stick-to-top {
        top: 0
    }
}

@media (max-width: 1199px) {
    .sidebar-navigation {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        width: 300px;
        -webkit-transform: translateX(-310px);
        transform: translateX(-310px);
        -webkit-transition: all .5s;
        transition: all .5s;
        z-index: 9999;
        -webkit-box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1);
        box-shadow: 3px 0 10px rgba(0, 0, 0, 0.1)
    }
}

.sidebar-navigation.show {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

@media (max-width: 767px) {
    .sidebar-navigation {
        width: 260px
    }
}

.doc-nav {
    padding-left: 0;
    list-style: none
}

.doc-nav>li {
    margin-bottom: 1.5rem
}

.doc-nav ul {
    padding-left: 3rem;
    list-style: none
}

.doc-nav ol {
    padding-left: 3rem
}

.doc-nav a {
    display: block;
    font-size: 16px;
    line-height: 3rem;
    color: #FFFFFF;
    text-decoration: none;
    padding: 0
}

.doc-nav a.active,
.doc-nav a:hover {
    color: var(--action-color)
}

.main {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    max-width: 100%;
    margin-left: 347px;
    padding: 0 0 30px 30px
}

@media (max-width: 1199px) {
    .main {
        margin: 60px 0 0;
        padding: 0
    }
}

.section-1,
.section-2 {
    height: 2000px
}

input,
textarea {
    width: 100%;
    padding: 1rem 2rem;
    background: #fff;
    border: 2px solid #eee;
    font-size: 16px;
    line-height: 30px;
    color: var(--typo-body)
}

.input-group {
    position: relative
}

.input-group button {
    position: absolute;
    background: transparent;
    border: none;
    top: 0;
    right: 0;
    font-size: 16px;
    line-height: 34px;
    padding: 1.5rem 2rem;
    cursor: pointer;
    -webkit-transition: all .5s;
    transition: all .5s
}

.input-group button i {
    font-weight: var(--w-medium);
    color: var(--typo-body);
    -webkit-transition: all .5s;
    transition: all .5s
}

.input-group button:hover i {
    color: var(--primary-color)
}

.main h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
    margin-bottom: 3rem;
    margin-top: 5rem;
    -webkit-transition: color .5s;
    transition: color .5s
}

.main h4 {
    margin: 0 0 2rem
}

.main p {
    margin-bottom: 2rem
}

.main ul,
.main ol {
    margin: 0 0 2rem;
    padding-left: 30px;
}

.main img {
    margin-bottom: 3rem;
    -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1)
}

img {
    max-width: 100%;
    height: auto
}

.page-footer {
    background-color: var(--typo-dark);
    padding: 19px 0;
    position: relative
}

.page-footer p {
    color: #fff;
    margin: 0
}

.page-footer a {
    color: #fff
}

.page-footer a:hover {
    color: var(--primary-color)
}

.nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

@media (max-width: 1199px) {
    .nav {
        display: block
    }
}

@media (max-width: 1199px) {
    .main-content {
        padding-top: 1px;
        padding-bottom: 50px
    }
}

section.active h3 {
    color: var(--action-color)
}

.sidebar-navigation ol {
    counter-reset: section;
    list-style-type: none
}

.sidebar-navigation ol li a::before {
    counter-increment: section;
    content: counters(section, ".") ". "
}

.back-to-top {
    position: fixed;
    bottom: -6rem;
    right: -6rem;
    display: block;
    width: 4rem;
    line-height: 4rem;
    background: var(--action-color);
    color: #fff;
    text-align: center;
    text-decoration: none;
    border-radius: var(--radius);
    opacity: 0;
    -webkit-transform: scale(0.3);
    transform: scale(0.3);
    -webkit-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.5);
    box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.5);
    z-index: 9;
    -webkit-transition: all .3s;
    transition: all .3s
}

.back-to-top.show {
    bottom: 3rem;
    right: 3rem;
    opacity: .7;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.back-to-top:hover {
    color: #fff;
    bottom: 3.2rem;
    opacity: 1
}

.nav-link:hover {
    text-decoration: underline
}

li img {
    display: block;
    margin-top: 20px
}

.sidebar-navigation .logo-wrapper img {
    max-height: 50px;
}



pre {
    text-align: left;
    white-space: pre-line;
    position: relative;
    padding: 5px 20px;
    background-color: #f5fcff;
    /* background-image: linear-gradient(transparent 50%, rgba(69, 142, 209, 0.04) 50%); */
    /* background-size: 3em 3em; */
    /* background-origin: content-box; */
    /* background-attachment: local; */
    margin: 15px 0px 40px;

}

code {
    font-weight: 600;
    line-height: 1.5;
}